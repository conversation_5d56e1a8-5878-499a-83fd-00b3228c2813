---
description: 
globs: 
alwaysApply: true
---
**schema.yml与parser之间关联与生成统一规则：**


-1 **是否差分的依据规则**

    当响应结果存在多种可能，则需要将schema进行差分，且不同的结果使用不同的parser;
   
-2 **差分文件内容及命名规则**

    差分中的diff_factors，属性值优先通过功能业务名命名（而不是写过多的参数放入差分，参数可以放在cmd的占位符中，调用会自动传入不同的参数），可以参考如下示例；

-3 **相关注释**

    需在小节开始添加”小节号-小节名“此类注释；


schema.yml内容示例：
```yml
    query_nvme_disk_info:
        key: "IPMI_查询NVME盘信息"
        variants:
        - diff_factors:
            - {name: "version", value: "default"}
            - {name: "operate", value: "query_nvme_list"}
            cmd: 'ipmitool -I lanplus -H "%(ip)s" -U "%(username)s" -P "%(password)s" raw 0x2e 0x12 0x3e 0x0f 0x00 0'
            parser: "IpmiNewOemNetfun0x2eParser.query_nvme_list"
        - diff_factors:
            - {name: "version", value: "V4"}
            - {name: "operate", value: "query_nvme_detail"}
            cmd: 'ipmitool -I lanplus -H "%(ip)s" -U "%(username)s" -P "%(password)s" raw 0x2e 0x12 0x3e 0x0f 0x00 1 %(filter_type)s %(device_form_type)s %(device_location)s'
            parser: "IpmiNewOemNetfun0x2eParser.query_nvme_detail"
```
    



