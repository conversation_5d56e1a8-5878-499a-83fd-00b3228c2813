---
description: 
globs: 
alwaysApply: false
---
命令响应字段解析规则(实现parser必遵守)：

- 1 **基础数据结构规则**
  
      无完成码：实际响应字段是没有完成码的，parser方法中不应该包含'完成码'的处理，所有响应数据都不包含第1位的完成码
      Manufacturer ID：固定3字节，小端字节序（Little Endian）
      格式：BYTE2 BYTE1 BYTE0 → 显示为 BYTE0BYTE1BYTE2
      示例：3e 0f 00 → 000f3e（ZTE Manufacturer ID）

- 2 **字段位置规则**

      所有字段位置相对于原设计整体前移1位
      原 raw_data[n] → 现 raw_data[n-1]（n≥1）
      原 raw_data[0]（完成码）→ 删除
- 3 **数据类型转换规则**
  
      16进制转文本：对于包含ASCII字符的字段，优先转换为可读文本
      可见字符过滤：只保留ASCII 32-126范围内的字符
      异常处理：转换失败时保留16进制格式
  
- 4 **具体字段映射示例**

      原始设计 → 实际实现
      raw_data[0] (完成码) → 删除
      raw_data[1] → raw_data[0] (Manufacturer ID BYTE0)
      raw_data[2] → raw_data[1] (Manufacturer ID BYTE1)  
      raw_data[3] → raw_data[2] (Manufacturer ID BYTE2)
      raw_data[4] → raw_data[3] (功能字段)
      raw_data[5] → raw_data[4] (功能字段)
      ...

- 5. **特殊字段处理规则**
  
      Manufacturer ID拼接：f"{raw_data[2]:02x}{raw_data[1]:02x}{raw_data[0]:02x}"
      文本字段：推测当前字段可能为文本字段的，需要转为文本，除了优先16进制转ASCII，失败时保留16进制

      数值字段：直接使用对应位置的字节值


 **响应数据的每一字节位的描述要参考引用的txt**
 **可参考引用目录“example”下的示例**