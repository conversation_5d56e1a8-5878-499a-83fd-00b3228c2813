---
description: 
globs: 
alwaysApply: true
---
# Robot Framework 自动化框架关键字生成规则

## 框架架构分层

本RF自动化框架采用分层架构设计，从下到上分为：

### 1. 基础设施层 (Infrastructure Layer)
- **位置**: [testlib/infrastructure/](mdc:testlib/infrastructure)
- **职责**: 
  - 设备连接和通信协议实现
  - 底层API封装和抽象
  - 通道管理 (SSH, FTP, HTTP等)
- **关键目录**:
  - `device/`: 设备抽象，如 [Bmc.py](mdc:testlib/infrastructure/device/bmc/Bmc.py)
  - `channels/`: 通信通道实现
  - `utility/`: 工具类和共享组件

### 2. 领域层 (Domain Layer)  
- **位置**: [testlib/domain/](mdc:testlib/domain)
- **职责**:
  - 业务逻辑封装和实现
  - 领域对象和服务
- **核心文件**: [Bmc.py](mdc:testlib/domain/bmc/Bmc.py) - 主要的BMC领域对象
- **组织方式**: 按业务领域分类 (bmc, host, network等)

### 3. 应用服务层 (Application Service Layer)
- **位置**: [testlib/appservice/](mdc:testlib/appservice)
- **职责**:
  - 用例级业务服务
  - 测试场景封装

### 4. 用户关键字层 (User Keywords Layer)
- **位置**: [userkeywords/](mdc:userkeywords)
- **职责**: 
  - Robot Framework关键字定义
  - 测试用例直接调用的接口
- **分类**:
  - `basic/`: 基础关键字，如 [bmc.robot](mdc:userkeywords/basic/bmc/bmc.robot)
  - `domain/`: 领域关键字，高级业务逻辑封装

## 关键字封装原则

### 1. 分层封装原则
- **基础关键字**: 直接调用应用服务层API，参数验证和错误处理
- **领域关键字**: 组合多个基础关键字，实现复杂业务场景
- **资源文件**: 使用 [resource.robot](mdc:userkeywords/basic/resource.robot) 管理依赖关系

### 2. 命名规范
- **中文命名**: 关键字名称使用中文，便于理解和维护
- **动词开头**: 如"创建BMC"、"查询系统日志"、"修改服务配置"
- **参数命名**: 使用驼峰命名，如 `${bmcAlias}`, `${protocolName}`

### 3. 参数设计
- **必选参数**: 放在前面，如设备别名
- **可选参数**: 提供默认值，如协议类型默认值
- **参数验证**: 在关键字开头进行参数校验

### 4. 文档规范
```robot
[Documentation]    [功能说明]
...    功能描述
...    
...    [入参]
...    ${参数名} - 参数说明
...    
...    [返回]  
...    返回值说明
...    
...    作者：姓名
...    编写日期：YYYY-MM-DD
```

### 5. 错误处理
- 使用 `should be true` 等Robot Framework内置关键字进行断言
- 提供清晰的错误信息
- 对于查询类关键字，返回有意义的数据结构

## 自动生成关键字指导

### 1. 识别生成需求
当需要为新的业务功能生成关键字时，按以下步骤：

1. **确定层次**: 基础关键字 vs 领域关键字
2. **分析依赖**: 查看 [testlib/appservice/](mdc:testlib/appservice) 中对应的服务类
3. **确定分类**: 根据功能领域选择存放目录

### 2. 模板结构
```robot
*** Settings ***
Library           testlib.appservice.模块.服务类
Library           Collections
Library           String

*** Keywords ***
关键字名称
    [Arguments]    ${必选参数}    ${可选参数}=默认值
    [Documentation]    按上述文档规范编写
    ${result}    调用应用服务方法    ${参数}
    处理返回结果和断言
    [Return]    ${result}
```

### 3. 代码生成规则
- **服务调用**: 直接调用 `testlib.appservice` 中对应的服务方法
- **参数映射**: Robot Framework参数直接传递给Python服务方法
- **返回值处理**: 根据业务需要决定是否需要返回值
- **异常处理**: 使用Robot Framework的断言关键字

### 4. 资源管理
- 新建模块时，需要更新对应的 `resource.robot` 文件
- 遵循现有的资源引用模式，如 [basic/resource.robot](mdc:userkeywords/basic/resource.robot)

### 5. 版本兼容性
- 考虑BMC V3/V4版本差异
- 在关键字实现中处理协议版本兼容性
- 使用 `获取BMC版本` 关键字进行版本判断

## 最佳实践

### 1. 重用性设计
- 抽取公共操作为独立关键字
- 使用参数化设计支持多种场景
- 避免硬编码，使用配置化参数

### 2. 可维护性
- 保持关键字功能单一
- 合理的抽象层次，避免过度封装
- 及时更新文档和注释

### 3. 测试友好
- 提供足够的调试信息
- 支持多种协议和通道
- 考虑并发执行的安全性

### 4. 性能考虑
- 避免不必要的重复操作
- 合理使用缓存机制
- 考虑超时和重试策略

通过遵循这些规则，可以确保生成的关键字具有良好的一致性、可维护性和可重用性。
